document.addEventListener('DOMContentLoaded', function () {
    // --- <PERSON>le Sacar de Inventario Modal ---
    var confirmarSacarBtn = document.getElementById('confirmarSacarBtn');
    var sacarForm         = document.getElementById('sacarForm');
    var sacarErrorDiv     = document.getElementById('sacarError');
    var spinnerSacar      = document.getElementById('spinnerSacar');
    
    if (confirmarSacarBtn && sacarForm) {
        confirmarSacarBtn.addEventListener('click', function () {
            // Reset error state
            if (sacarErrorDiv) {
                sacarErrorDiv.classList.add('d-none');
            }
            
            // Basic validation mirroring sinventario requirements
            var contId = document.getElementById('sacar_id_contenedor').value;
            var actId  = document.getElementById('sacar_id_activo').value;
            if (!contId || !actId) {
                if (sacarErrorDiv) {
                    sacarErrorDiv.textContent = 'No se pudo determinar contenedor o activo.';
                    sacarErrorDiv.classList.remove('d-none');
                }
                return;
            }
            
            // Show loading spinner
            if (spinnerSacar) {
                spinnerSacar.classList.remove('d-none');
            }
            confirmarSacarBtn.disabled = true;
            
            sacarForm.submit();
        });
    }
    
    // --- Handle Agregar a Contenedor Modal ---
    var confirmarAgregarBtn = document.getElementById('confirmarAgregarBtn');
    var agregarForm         = document.getElementById('agregarForm');
    var agregarErrorDiv     = document.getElementById('agregarError');
    var spinnerAgregar      = document.getElementById('spinnerAgregar');
    
    if (confirmarAgregarBtn && agregarForm) {
        confirmarAgregarBtn.addEventListener('click', function () {
            // Reset error state
            if (agregarErrorDiv) {
                agregarErrorDiv.classList.add('d-none');
            }
            
            // Validate container selection
            var contenedorId = document.getElementById('agregar_id_contenedor').value;
            if (!contenedorId) {
                if (agregarErrorDiv) {
                    agregarErrorDiv.textContent = 'Por favor seleccione un contenedor.';
                    agregarErrorDiv.classList.remove('d-none');
                }
                return;
            }
            
            // Update hidden form field
            document.getElementById('agregar_form_contenedor').value = contenedorId;
            
            // Show loading spinner
            if (spinnerAgregar) {
                spinnerAgregar.classList.remove('d-none');
            }
            confirmarAgregarBtn.disabled = true;
            
            // Submit form
            agregarForm.submit();
        });
    }
});
