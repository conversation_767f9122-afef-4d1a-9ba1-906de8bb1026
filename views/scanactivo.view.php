<?php
#region region DOCS

/** @var Activo|null $activo */
/** @var array $imagenes */
/** @var string $error_display */
/** @var string $error_text */
/** @var Inventario|null $inventario_estado */

/** @var array $contenedores */

use App\classes\Activo;
use App\classes\Inventario;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Información del Activo</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
	
	<!-- Custom styles for scanactivo -->
	<link href="<?php echo RUTA_RESOURCES; ?>css/scanactivo.css" rel="stylesheet"/>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Información del Activo</h4>
				<p class="mb-0 text-muted">Detalles del activo escaneado</p>
			</div>
			<div class="ms-auto">
				<a href="lactivos" class="btn btn-secondary me-2">
					<i class="fa fa-list fa-fw me-1"></i> Listado de Activos
				</a>
				<a href="eactivo?id=<?php echo (int)($activo?->getId() ?? 0); ?>" class="btn btn-primary">
					<i class="fa fa-edit fa-fw me-1"></i> Editar Activo
				</a>
			</div>
		</div>
		
		<hr>
		<?php #endregion PAGE HEADER ?>
		
		<?php #region region PANEL ACTIVO ?>
		<div class="panel panel-inverse mt-3">
			<div class="panel-heading">
				<h4 class="panel-title">
					Información del Activo
				</h4>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="panel-body">
				<!-- Basic Information -->
				<div class="row">
					<div class="col-md-12">
						<div class="mb-3">
							<label class="form-label text-info"><strong>Descripción:</strong></label>
							<p class="form-control-static"><?php echo htmlspecialchars($activo->getDescripcion() ?? ''); ?></p>
						</div>
					</div>
				</div>
				
				<!-- Brand and Model -->
				<div class="row">
					<div class="col-md-6">
						<div class="mb-3">
							<label class="form-label text-info"><strong>Marca:</strong></label>
							<p class="form-control-static"><?php echo htmlspecialchars($activo->getMarca() ?? 'No especificada'); ?></p>
						</div>
					</div>
					<div class="col-md-6">
						<div class="mb-3">
							<label class="form-label text-info"><strong>Modelo:</strong></label>
							<p class="form-control-static"><?php echo htmlspecialchars($activo->getModelo() ?? 'No especificado'); ?></p>
						</div>
					</div>
				</div>
				
				<!-- Serial Number and Acquisition Date -->
				<div class="row">
					<div class="col-md-6">
						<div class="mb-3">
							<label class="form-label text-info"><strong>Número de Serie:</strong></label>
							<p class="form-control-static"><?php echo htmlspecialchars($activo->getNumeroSerie() ?? 'No especificado'); ?></p>
						</div>
					</div>
					<div class="col-md-6">
						<div class="mb-3">
							<label class="form-label text-info"><strong>Fecha de Adquisición:</strong></label>
							<p class="form-control-static">
								<?php
								$fechaAdquisicion = $activo->getFechaAdquisicion();
								if ($fechaAdquisicion && $fechaAdquisicion !== '0000-00-00' && $fechaAdquisicion !== '') {
									echo htmlspecialchars($fechaAdquisicion);
								} else {
									echo 'N/A';
								}
								?>
							</p>
						</div>
					</div>
				</div>
				
				<!-- Asset Value -->
				<div class="row">
					<div class="col-md-6">
						<div class="mb-3">
							<label class="form-label text-info"><strong>Valor del Activo:</strong></label>
							<p class="form-control-static">
								<?php echo $activo->getValorActivo() ? '$' . number_format($activo->getValorActivo(), 0, '', '.') : 'No especificado'; ?>
							</p>
						</div>
					</div>
				</div>
				
				<!-- Characteristics -->
				<div class="row">
					<div class="col-md-12">
						<div class="mb-3">
							<label class="form-label text-info"><strong>Características:</strong></label>
							<p class="form-control-static"><?php echo nl2br(htmlspecialchars($activo->getCaracteristicas() ?? 'No especificadas')); ?></p>
						</div>
					</div>
				</div>
				
				<!-- Comments -->
				<div class="row">
					<div class="col-md-12">
						<div class="mb-3">
							<label class="form-label text-info"><strong>Comentarios:</strong></label>
							<p class="form-control-static"><?php echo nl2br(htmlspecialchars($activo->getComentarios() ?? 'Sin comentarios')); ?></p>
						</div>
					</div>
				</div>
				
				<!-- Inventory Status (moved and styled) -->
				<div class="bg-info bg-opacity-10 border-start border-info border-4 rounded p-3 mt-3">
					<div class="row mb-2">
						<div class="col-12 d-flex align-items-center">
							<h3 class="mb-2 text-white-500">Estado de Inventario</h3>
						</div>
					</div>
					<div class="row">
						<?php if (!empty($inventario_estado)): ?>
							<div class="col-12 col-sm-6 col-md-4">
								<div class="mb-3">
									<label class="form-label text-info"><strong>Contenedor asignado:</strong></label>
									<p class="form-control-static"><?php echo htmlspecialchars($inventario_estado->getContenedor_descripcion() ?? 'N/A'); ?></p>
								</div>
							</div>
							<div class="col-12 col-sm-6 col-md-4">
								<div class="mb-3">
									<label class="form-label text-info"><strong>Estado:</strong></label>
									<p class="form-control-static">
										<?php if ((int)$inventario_estado->getEn_contenedor() === 1): ?>
											<span class="badge bg-success">No prestado</span>
										<?php else: ?>
											<span class="badge bg-danger">Prestado</span>
										<?php endif; ?>
									</p>
								</div>
							</div>
							<div class="col-12 col-sm-12 col-md-4">
								<div class="mb-3">
									<label class="form-label text-info"><strong>Trabajador asignado:</strong></label>
									<p class="form-control-static">
										<?php echo $inventario_estado->getTrabajador_nombre()
												? htmlspecialchars($inventario_estado->getTrabajador_cedula() . ' - ' . $inventario_estado->getTrabajador_nombre())
												: '-- No asignado --'; ?>
									</p>
								</div>
							</div>
						<?php else: ?>
							<div class="col-12 col-sm-12 col-md-12 mb-3">
								<div class="alert alert-secondary mb-0">
									<i class="fa fa-info-circle me-2"></i> Este activo no tiene un registro de inventario. No está asignado a un contenedor.
								</div>
							</div>
						<?php endif; ?>
						
						<!-- Actions: Sacar de Inventario / Agregar a Contenedor -->
						<div class="row">
							<div class="col-3">
								<button type="button" class="btn btn-danger w-100" id="btn-sacar-de-inventario"
								        data-bs-toggle="modal" data-bs-target="#modal-confirm-sacar"
										<?php echo (!empty($inventario_estado) && $inventario_estado->getEn_contenedor() === 1) ? '' : 'disabled'; ?>>
									<span id="spinnerSacar" class="spinner-border spinner-border-sm d-none me-1" role="status"></span>
									<i class="fa fa-minus fa-fw me-1"></i> Sacar de contenedor
								</button>
							</div>
							<div class="col-3">
								<button type="button" class="btn btn-success w-100" id="btn-agregar-a-contenedor"
								        data-bs-toggle="modal" data-bs-target="#modal-agregar-contenedor"
										<?php echo empty($inventario_estado) ? '' : 'disabled'; ?>>
									<span id="spinnerAgregar" class="spinner-border spinner-border-sm d-none me-1" role="status"></span>
									<i class="fa fa-plus fa-fw me-1"></i> Agregar a Contenedor
								</button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<?php #endregion PANEL ACTIVO ?>
		
		<?php #region region IMAGES ?>
		<div class="panel panel-inverse mt-3">
			<div class="panel-heading">
				<h4 class="panel-title">
					Imágenes del Activo
				</h4>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="panel-body">
				<?php if (empty($imagenes)): ?>
					<div class="alert alert-info">
						<i class="fa fa-info-circle me-2"></i> Este activo no tiene imágenes asociadas.
					</div>
				<?php else: ?>
					<div class="row">
						<?php foreach ($imagenes as $imagen): ?>
							<div class="col-md-3 col-sm-4 col-6 mb-3">
								<div class="image-container text-center">
									<img src="<?php echo RUTA; ?>resources/uploads/activos/<?php echo htmlspecialchars($imagen->getNombreArchivo()); ?>"
									     alt="Imagen de activo" class="img-fluid img-thumbnail" style="max-height: 200px; width: auto;">
									<div class="image-caption text-center mt-2">
										<small class="text-muted"><?php echo htmlspecialchars($imagen->getNombreArchivo()); ?></small>
									</div>
								</div>
							</div>
						<?php endforeach; ?>
					</div>
				<?php endif; ?>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion IMAGES ?>
		
		<!-- BEGIN scroll-top-btn -->
		<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
		<!-- END scroll-top-btn -->
		
		<!-- Confirmation Modal -->
		<div class="modal fade" id="modal-confirm-sacar" tabindex="-1" aria-labelledby="modalConfirmSacarLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="modalConfirmSacarLabel">Confirmar acción</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						¿Está seguro de sacar este activo del contenedor?
						<div class="small text-muted mt-2">
							Esta acción eliminará la asociación del activo con el contenedor actual y registrará el movimiento como salida.
						</div>
						<div id="sacarError" class="text-danger d-none mt-2"></div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
						<button type="button" class="btn btn-danger" id="confirmarSacarBtn">
							<i class="fa fa-check me-1"></i> Confirmar
						</button>
					</div>
				</div>
			</div>
		</div>
		
		<!-- Modal: Agregar a Contenedor -->
		<div class="modal fade" id="modal-agregar-contenedor" tabindex="-1" aria-labelledby="modalAgregarContenedorLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="modalAgregarContenedorLabel">Agregar a Contenedor</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<p>Seleccione el contenedor donde desea agregar este activo:</p>
						<div class="mb-3">
							<label for="agregar_id_contenedor" class="form-label">Contenedor: <span class="text-danger">*</span></label>
							<select class="form-select" id="agregar_id_contenedor" name="id_contenedor" required>
								<option value="">-- Seleccione un contenedor --</option>
								<?php if (!empty($contenedores)): ?>
									<?php foreach ($contenedores as $contenedor): ?>
										<option value="<?php echo $contenedor->getId(); ?>">
											<?php echo htmlspecialchars($contenedor->getDescripcion()); ?>
										</option>
									<?php endforeach; ?>
								<?php endif; ?>
							</select>
							<div class="invalid-feedback">Por favor seleccione un contenedor.</div>
						</div>
						<div class="small text-muted">
							Esta acción creará una nueva asociación del activo con el contenedor seleccionado y registrará el movimiento como entrada.
						</div>
						<div id="agregarError" class="text-danger d-none mt-2"></div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
						<button type="button" class="btn btn-success" id="confirmarAgregarBtn">
							<i class="fa fa-check me-1"></i> Confirmar
						</button>
					</div>
				</div>
			</div>
		</div>
		
		<!-- Hidden form to reuse sinventario backend -->
		<form id="sacarForm" action="sinventario" method="POST" class="d-none">
			<input type="hidden" name="id_contenedor" id="sacar_id_contenedor" value="<?php echo (int)($inventario_estado?->getId_contenedor() ?? 0); ?>">
			<input type="hidden" name="activos[]" id="sacar_id_activo" value="<?php echo (int)($inventario_estado?->getId_activo() ?? 0); ?>">
			<input type="hidden" name="redirect_url" value="scanactivo?id=<?php echo (int)($activo?->getId() ?? 0); ?>">
		</form>
		
		<!-- Hidden form to reuse iinventario backend -->
		<form id="agregarForm" action="iinventario" method="POST" class="d-none">
			<input type="hidden" name="id_contenedor" id="agregar_form_contenedor" value="">
			<input type="hidden" name="activos[]" value="<?php echo (int)($activo?->getId() ?? 0); ?>">
			<input type="hidden" name="redirect_url" value="scanactivo?id=<?php echo (int)($activo?->getId() ?? 0); ?>">
		</form>
	</div>
	<!-- END #content -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<script src="<?php echo RUTA_RESOURCES ?>js/scanactivo.js"></script>
<!-- ================== END PAGE LEVEL JS ================== -->

<?php #endregion JS ?>

</body>
</html>
