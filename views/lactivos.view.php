<?php
#region region DOCS

/** @var Activo[] $activos */

use App\classes\Activo;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Activos</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Activos</h4>
				<p class="mb-0 text-muted">Administra los activos del sistema</p>
			</div>
			<div class="ms-auto">
				<a href="iactivo" class="btn btn-success"><i class="fa fa-plus-circle fa-fw me-1"></i> Crear nuevo</a>
			</div>
		</div>
		
		<hr>
		<?php #endregion PAGE HEADER ?>
		
		<?php #region region PANEL ACTIVOS ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Listado de Activos
				</h4>
				<div class="ms-auto">
					<?php if (!empty($activos)): ?>
						<button type="button" id="export-activos-btn" class="btn btn-sm btn-primary" title="Exportar a Excel">
							<i class="fa fa-file-excel fa-fw me-1"></i> Exportar a Excel
						</button>
					<?php endif; ?>
				</div>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="p-1 table-nowrap" style="overflow: auto">
				<?php #region region TABLE ACTIVOS ?>
				<table class="table table-hover table-sm">
					<thead>
					<tr>
						<th>Acciones</th>
						<th>Descripción</th>
						<th>Marca</th>
						<th>Modelo</th>
						<th>Número Serie</th>
						<th class="text-center">Fecha Adquisición</th>
						<th class="text-center">Valor Activo</th>
					</tr>
					</thead>
					<tbody class="fs-12px" id="activo-table-body">
					<?php foreach ($activos as $activo): ?>
						<tr data-activo-id="<?php echo $activo->getId(); ?>">
							<td>
								<?php // Scan Button - Links to scanactivo page ?>
								<a href="scanactivo?id=<?php echo $activo->getId(); ?>" class="btn btn-xs btn-warning me-1"
								   title="Escanear Activo">
									<i class="fa fa-qrcode"></i>
								</a>
								<?php // Edit Button - Links to eactivo page ?>
								<a href="eactivo?id=<?php echo $activo->getId(); ?>" class="btn btn-xs btn-primary me-1"
								   title="Editar Activo">
									<i class="fa fa-edit"></i>
								</a>
								<?php // View Images Button ?>
								<button type="button" class="btn btn-xs btn-info me-1 btn-ver-imagenes"
								        title="Ver Imágenes"
								        data-activoid="<?php echo $activo->getId(); ?>"
								        data-descripcion="<?php echo htmlspecialchars($activo->getDescripcion() ?? ''); ?>">
									<i class="fa fa-images"></i>
								</button>
								<?php // Deactivate/Activate Button ?>
								<?php if ($activo->isActivo()): ?>
									<button type="button" class="btn btn-xs btn-danger btn-desactivar-activo"
									        title="Desactivar"
									        data-activoid="<?php echo $activo->getId(); ?>"
									        data-descripcion="<?php echo htmlspecialchars($activo->getDescripcion() ?? ''); ?>">
										<i class="fa fa-trash-alt"></i>
									</button>
								<?php else: ?>
									<button type="button" class="btn btn-xs btn-success btn-activar-activo"
									        title="Activar"
									        data-activoid="<?php echo $activo->getId(); ?>"
									        data-descripcion="<?php echo htmlspecialchars($activo->getDescripcion() ?? ''); ?>">
										<i class="fa fa-check"></i>
									</button>
								<?php endif; ?>
							</td>
							<td><?php echo htmlspecialchars($activo->getDescripcion()); ?></td>
							<td><?php echo htmlspecialchars($activo->getMarca() ?? ''); ?></td>
							<td><?php echo htmlspecialchars($activo->getModelo() ?? ''); ?></td>
							<td><?php echo htmlspecialchars($activo->getNumeroSerie() ?? ''); ?></td>
							<td class="text-center"><?php echo $activo->getFechaAdquisicion() ? $activo->getFechaAdquisicion() : ''; ?></td>
							<td class="text-end"><?php echo $activo->getValorActivo() ? '$' . number_format($activo->getValorActivo(), 0, '', '.') : ''; ?></td>
						</tr>
					<?php endforeach; ?>
					<?php if (empty($activos)): ?>
						<tr>
							<td colspan="8" class="text-center">No hay activos para mostrar.</td>
						</tr>
					<?php endif; ?>
					
					</tbody>
				</table>
				<?php #endregion TABLE ACTIVOS ?>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion PANEL ACTIVOS ?>
	
	</div>
	<!-- END #content -->
	
	<!-- Modal for displaying images -->
	<div class="modal fade" id="imagenes-modal" tabindex="-1" role="dialog" aria-labelledby="imagenes-modal-label" aria-hidden="true">
		<div class="modal-dialog modal-lg" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="imagenes-modal-label">Imágenes del Activo</h5>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<div id="imagenes-container" class="d-flex flex-wrap gap-2">
						<!-- Images will be loaded here -->
					</div>
					<div id="no-imagenes-message" class="text-center p-3 d-none">
						<p>Este activo no tiene imágenes asociadas.</p>
					</div>
					<div id="loading-imagenes" class="text-center p-3">
						<span class="spinner-border spinner-border-sm"></span> Cargando imágenes...
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
				</div>
			</div>
		</div>
	</div>
	
	<?php #region region Hidden Forms for Activation/Deactivation ?>
	<form id="deactivate-activo-form" method="POST" action="lactivos" style="display: none;">
		<input type="hidden" name="action" value="desactivar">
		<input type="hidden" name="activoId" id="deactivate-activo-id">
	</form>
	
	<form id="activate-activo-form" method="POST" action="lactivos" style="display: none;">
		<input type="hidden" name="action" value="activar">
		<input type="hidden" name="activoId" id="activate-activo-id">
	</form>
	<?php #endregion Hidden Forms ?>
	
	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<script src="<?php echo RUTA_RESOURCES ?>js/lactivos.js"></script>
<!-- ================== END PAGE LEVEL JS ================== -->

<?php #endregion JS ?>

</body>
</html>
